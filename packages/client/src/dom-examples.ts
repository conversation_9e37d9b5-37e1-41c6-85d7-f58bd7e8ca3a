/**
 * DOM工具库使用示例
 */

import {
  query,
  queryAll,
  addClass,
  removeClass,
  toggleClass,
  setActiveByIndex,
  setMultipleActiveByIndex,
  setStyle,
  setAttr,
  removeAttr,
  setText,
  setHtml,
  on,
  off,
  show,
  hide
} from './dom'

// 示例1: 你原来的代码简化
// 原来的代码:
// colorTitles.forEach((title) => title.classList.remove('active'))
// colorTexts.forEach((text) => text.classList.remove('active'))
// colorTitles[curIdx].classList.add('active')
// colorTexts[curIdx].classList.add('active')

// 使用新工具库的方式1 - 分别操作:
function updateActiveState1(curIdx: number) {
  const colorTitles = queryAll('.color-title')
  const colorTexts = queryAll('.color-text')

  setActiveByIndex(colorTitles, 'active', curIdx)
  setActiveByIndex(colorTexts, 'active', curIdx)
}

// 使用新工具库的方式2 - 批量操作:
function updateActiveState2(curIdx: number) {
  setMultipleActiveByIndex([
    { elements: '.color-title', className: 'active' },
    { elements: '.color-text', className: 'active' }
  ], curIdx)
}

// 示例2: 基础DOM操作
function basicDomOperations() {
  // 获取元素
  const element = query('.my-element')
  const elements = queryAll('.my-elements')

  // 类名操作
  addClass('.buttons', 'btn-primary')
  removeClass('.old-buttons', 'deprecated')
  toggleClass('.toggle-buttons', 'expanded')

  // 样式操作
  setStyle('.cards', {
    backgroundColor: '#f0f0f0',
    padding: '20px',
    borderRadius: '8px'
  })

  // 属性操作
  setAttr('.links', {
    'data-track': 'true',
    'target': '_blank'
  })

  removeAttr('.forms', 'disabled', 'readonly')

  // 内容操作
  setText('.counters', '0')
  setHtml('.containers', '<div class="content">新内容</div>')

  // 显示/隐藏
  show('.hidden-elements')
  hide('.visible-elements')
}

// 示例3: 事件处理
function eventHandling() {
  // 批量添加点击事件
  on('.buttons', 'click', (e) => {
    console.log('按钮被点击:', e.target)
  })

  // 批量添加鼠标悬停事件
  on('.cards', 'mouseenter', (e) => {
    addClass([e.target as HTMLElement], 'hovered')
  })

  on('.cards', 'mouseleave', (e) => {
    removeClass([e.target as HTMLElement], 'hovered')
  })
}

// 示例4: 标签页切换
function setupTabs() {
  const tabButtons = queryAll('.tab-button')
  const tabContents = queryAll('.tab-content')

  on('.tab-button', 'click', (e) => {
    const clickedButton = e.target as HTMLElement
    const tabIndex = Array.from(tabButtons).indexOf(clickedButton)

    // 使用工具库简化标签页切换
    setMultipleActiveByIndex([
      { elements: tabButtons, className: 'active' },
      { elements: tabContents, className: 'active' }
    ], tabIndex)
  })
}

// 示例5: 轮播图控制
function setupCarousel() {
  const slides = queryAll('.slide')
  const indicators = queryAll('.indicator')
  let currentSlide = 0

  function goToSlide(index: number) {
    setMultipleActiveByIndex([
      { elements: slides, className: 'active' },
      { elements: indicators, className: 'active' }
    ], index)

    currentSlide = index
  }

  // 下一张
  function nextSlide() {
    const nextIndex = (currentSlide + 1) % slides.length
    goToSlide(nextIndex)
  }

  // 上一张
  function prevSlide() {
    const prevIndex = (currentSlide - 1 + slides.length) % slides.length
    goToSlide(prevIndex)
  }

  // 绑定控制按钮
  on('.carousel-next', 'click', nextSlide)
  on('.carousel-prev', 'click', prevSlide)

  // 绑定指示器点击
  on('.indicator', 'click', (e) => {
    const clickedIndicator = e.target as HTMLElement
    const index = Array.from(indicators).indexOf(clickedIndicator)
    goToSlide(index)
  })
}

// 示例6: 表单验证状态
function setupFormValidation() {
  const inputs = queryAll('.form-input')

  on('.form-input', 'blur', (e) => {
    const input = e.target as HTMLInputElement
    const isValid = input.value.trim() !== ''

    if (isValid) {
      removeClass([input], 'error')
      addClass([input], 'valid')
    } else {
      removeClass([input], 'valid')
      addClass([input], 'error')
    }
  })
}

// 示例7: 手风琴组件
function setupAccordion() {
  on('.accordion-header', 'click', (e) => {
    const header = e.target as HTMLElement
    const content = header.nextElementSibling as HTMLElement
    const accordion = header.closest('.accordion-item') as HTMLElement

    // 切换当前项
    toggleClass([accordion], 'expanded')

    // 如果需要只展开一个，可以先关闭其他项
    const allItems = queryAll('.accordion-item')
    Array.from(allItems).forEach(item => {
      if (item !== accordion) {
        removeClass([item], 'expanded')
      }
    })
  })
}

export {
  updateActiveState1,
  updateActiveState2,
  basicDomOperations,
  eventHandling,
  setupTabs,
  setupCarousel,
  setupFormValidation,
  setupAccordion
}
