/**
 * DOM操作工具库
 * 提供简化的DOM操作方法
 */

/**
 * 获取单个元素
 */
export function query(selector: string): HTMLElement
export function query(element: ParentNode, selector: string): HTMLElement
export function query(
  elementOrSelector: ParentNode | string,
  selector?: string,
): HTMLElement {
  if (typeof elementOrSelector === 'string') {
    return query(document, elementOrSelector)
  }
  if (!selector) {
    throw new Error('selector is required')
  }
  return elementOrSelector.querySelector(selector) as HTMLElement
}

/**
 * 获取多个元素
 */
export function queryAll(selector: string): HTMLElement[]
export function queryAll(element: ParentNode, selector: string): HTMLElement[]
export function queryAll(
  elementOrSelector: ParentNode | string,
  selector?: string,
): HTMLElement[] {
  if (typeof elementOrSelector === 'string') {
    return queryAll(document, elementOrSelector)
  }

  if (!selector) {
    throw new Error('selector is required')
  }

  return Array.from(elementOrSelector.querySelectorAll(selector))
}

export function addClass(
  target: HTMLElement | HTMLElement[],
  classNames: string[],
): void {
  const elements = resolveElements(target)
  elements.forEach((el) => el.classList.add(...classNames))
}

export function removeClass(
  target: HTMLElement | HTMLElement[],
  classNames: string[],
): void {
  const elements = resolveElements(target)
  elements.forEach((el) => el.classList.remove(...classNames))
}

export function toggleClass(
  target: HTMLElement | HTMLElement[],
  className: string,
  force?: boolean,
): void {
  const elements = resolveElements(target)
  elements.forEach((el) => el.classList.toggle(className, force))
}

/**
 * 移除元素的类名，然后给指定索引的元素添加类名
 */
export function setActiveByIndex(
  target: HTMLElement | HTMLElement[],
  classNames: string,
  activeIndex: number,
): void {
  const elements = resolveElements(target)
  elements.forEach((el) => el.classList.remove(...classNames))

  if (elements[activeIndex]) {
    elements[activeIndex].classList.add(...classNames)
  }
}

/**
 * 同时操作多组元素的激活状态
 */
export function setMultipleActiveByIndex(
  groups: { elements: HTMLElement[]; className: string }[],
  activeIndex: number,
): void {
  groups.forEach(({ elements, className }) => {
    setActiveByIndex(elements, className, activeIndex)
  })
}

/**
 * 批量设置样式
 */
export function setStyle(
  target: HTMLElement | HTMLElement[],
  styles: Partial<CSSStyleDeclaration>,
): void {
  const elements = resolveElements(target)
  elements.forEach((el) => {
    Object.assign(el.style, styles)
  })
}

/**
 * 批量设置属性
 */
export function setAttr(
  target: HTMLElement | HTMLElement[],
  attributes: Record<string, string>,
): void {
  const elements = resolveElements(target)
  elements.forEach((el) => {
    Object.entries(attributes).forEach(([key, value]) => {
      el.setAttribute(key, value)
    })
  })
}

/**
 * 批量移除属性
 */
export function removeAttr(
  target: HTMLElement | HTMLElement[],
  ...attributes: string[]
): void {
  const elements = resolveElements(target)
  elements.forEach((el) => {
    attributes.forEach((attr) => el.removeAttribute(attr))
  })
}

const listenerMap = new WeakMap<Function, EventListenerOrEventListenerObject>()

export function on<T extends EventTarget, K extends keyof HTMLElementEventMap>(
  target: T | T[],
  type: K,
  listener: (this: T, event: HTMLElementEventMap[K], index: number) => void,
  options?: boolean | AddEventListenerOptions,
): void {
  const elements = resolveElements(target)

  elements.forEach((el, index) => {
    if (listenerMap.has(listener)) {
      const wrappedListener = listenerMap.get(listener)
      if (wrappedListener) {
        el.addEventListener(type, wrappedListener, options)
        return
      }
    }

    const wrappedListener = function (event: Event) {
      listener.call(el, event as HTMLElementEventMap[K], index)
    }

    listenerMap.set(listener, wrappedListener)

    el.addEventListener(type, wrappedListener, options)
  })
}

export function off<T extends EventTarget, K extends keyof HTMLElementEventMap>(
  target: T | T[],
  type: K,
  listener: (this: T, event: HTMLElementEventMap[K], index: number) => void,
  options?: boolean | EventListenerOptions,
): void {
  const elements = resolveElements(target)

  const wrappedListener = listenerMap.get(listener)

  if (!wrappedListener) {
    return
  }

  elements.forEach((el) => {
    el.removeEventListener(type, wrappedListener, options)
  })
}

/**
 * 存储元素原始 display 值的 WeakMap
 */
const originalDisplayMap = new WeakMap<HTMLElement, string>()

/**
 * 显示元素，恢复到原来的 display 值
 */
export function show(target: HTMLElement | HTMLElement[]): void {
  const elements = resolveElements(target)
  elements.forEach((el) => {
    // 如果有存储的原始值，使用它
    const originalDisplay = originalDisplayMap.get(el)
    if (originalDisplay) {
      el.style.display = originalDisplay
      originalDisplayMap.delete(el)
    } else {
      el.style.display = ''
    }
  })
}

/**
 * 隐藏元素，保存原始的 display 值
 */
export function hide(target: HTMLElement | HTMLElement[]): void {
  const elements = resolveElements(target)
  elements.forEach((el) => {
    if (el.style.display !== 'none') {
      const currentDisplay = el.style.display
      originalDisplayMap.set(el, currentDisplay)
    }

    el.style.display = 'none'
  })
}

/**
 * 切换元素的显示/隐藏状态
 */
export function toggle(target: HTMLElement | HTMLElement[]): void {
  const elements = resolveElements(target)
  elements.forEach((el) => {
    const currentDisplay = getComputedDisplay(el)
    if (currentDisplay === 'none') {
      show(el)
    } else {
      hide(el)
    }
  })
}

function resolveElements(target: HTMLElement | HTMLElement[]) {
  return Array.isArray(target) ? target : [target]
}
