/**
 * DOM操作工具库
 * 提供简化的DOM操作方法
 */

// 类型定义
type ElementOrSelector = HTMLElement | string
type ElementsOrSelector = HTMLElement[] | string
type ElementTarget = HTMLElement | HTMLElement[]

/**
 * 获取单个元素
 */
export function query(selector: string): HTMLElement
export function query(element: ParentNode, selector: string): HTMLElement
export function query(
  elementOrSelector: ParentNode | string,
  selector?: string,
): HTMLElement {
  if (typeof elementOrSelector === 'string') {
    return query(document, elementOrSelector)
  }
  if (!selector) {
    throw new Error('selector is required')
  }
  return elementOrSelector.querySelector(selector) as HTMLElement
}

/**
 * 获取多个元素
 */
export function queryAll(selector: string): HTMLElement[]
export function queryAll(element: ParentNode, selector: string): HTMLElement[]
export function queryAll(
  elementOrSelector: ParentNode | string,
  selector?: string,
): HTMLElement[] {
  if (typeof elementOrSelector === 'string') {
    return queryAll(document, elementOrSelector)
  }

  if (!selector) {
    throw new Error('selector is required')
  }

  return Array.from(elementOrSelector.querySelectorAll(selector))
}


export function addClass(
  target: HTMLElement | HTMLElement[],
  classNames: string[],
): void {
  const elements = Array.isArray(target) ? target : [target]
  elements.forEach((el) => el.classList.add(...classNames))
}

export function removeClass(
  target: HTMLElement | HTMLElement[],
  classNames: string[],
): void {
  const elements = Array.isArray(target) ? target : [target]
  elements.forEach((el) => el.classList.remove(...classNames))
}

export function toggleClass(
  target: HTMLElement | HTMLElement[],
  className: string,
  force?: boolean,
): void {
  const elements = Array.isArray(target) ? target : [target]
  elements.forEach((el) => el.classList.toggle(className, force))
}

/**
 * 移除元素的类名，然后给指定索引的元素添加类名
 */
export function setActiveByIndex(
  target: HTMLElement | HTMLElement[],
  classNames: string,
  activeIndex: number,
): void {
  const elements = Array.isArray(target) ? target : [target]
  elements.forEach((el) => el.classList.remove(...classNames))

  if (elements[activeIndex]) {
    elements[activeIndex].classList.add(...classNames)
  }
}

/**
 * 同时操作多组元素的激活状态
 */
export function setMultipleActiveByIndex(
  groups: { elements: HTMLElement[]; className: string }[],
  activeIndex: number,
): void {
  groups.forEach(({ elements, className }) => {
    setActiveByIndex(elements, className, activeIndex)
  })
}

/**
 * 批量设置样式
 */
export function setStyle(
  target: HTMLElement | HTMLElement[],
  styles: Partial<CSSStyleDeclaration>,
): void {
  const elements = Array.isArray(target) ? target : [target]
  elements.forEach((el) => {
    Object.assign(el.style, styles)
  })
}

/**
 * 批量设置属性
 */
export function setAttr(
  target: HTMLElement | HTMLElement[],
  attributes: Record<string, string>,
): void {
  const elements = Array.isArray(target) ? target : [target]
  elements.forEach((el) => {
    Object.entries(attributes).forEach(([key, value]) => {
      el.setAttribute(key, value)
    })
  })
}


/**
 * 批量移除属性
 */
export function removeAttr(
  target: HTMLElement | HTMLElement[],
  ...attributes: string[]
): void {
  const elements = Array.isArray(target) ? target : [target]
  elements.forEach((el) => {
    attributes.forEach((attr) => el.removeAttribute(attr))
  })
}

/**
 * 批量添加事件监听器
 */

export function on<T extends EventTarget, K extends keyof HTMLElementEventMap>(
  target: T | T[],
  type: K,
  listener: (this: T, event: HTMLElementEventMap[K], index: number) => void,
  options?: boolean | AddEventListenerOptions,
): void {
  const elements = Array.isArray(target) ? target : [target]
  elements.forEach((el, index) => {
    el.addEventListener(type, function (event) {
      listener.call(el, event as HTMLElementEventMap[K], index)
    }, options)
  })
}

/**
 * 批量移除事件监听器
 */
export function off<T extends EventTarget, K extends keyof HTMLElementEventMap>(
  target: T | T[],
  type: K,
  listener: (this: T, event: HTMLElementEventMap[K], index: number) => void,
  options?: boolean | EventListenerOptions,
): void {
  const elements = Array.isArray(target) ? target : [target]
  elements.forEach((el) => {
    el.removeEventListener(type, listener as EventListener, options)
  })
}
//
// /**
//  * 显示元素
//  */
// export function show(elementsOrSelector: ElementsOrSelector): void {
//   const elements = queryAll(elementsOrSelector)
//   Array.from(elements).forEach((el) => {
//     el.style.display = ''
//   })
// }
//
// /**
//  * 隐藏元素
//  */
// export function hide(elementsOrSelector: ElementsOrSelector): void {
//   const elements = queryAll(elementsOrSelector)
//   Array.from(elements).forEach((el) => {
//     el.style.display = 'none'
//   })
// }
